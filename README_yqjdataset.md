# YOLOv5 + YQJDataset 使用指南

本项目已将YOLOv5配置为使用yqjdataset数据集进行训练和推理。

## 数据集信息

- **训练集**: 4161张图像 (`yqjdataset/train/images`)
- **测试集**: 1040张图像 (`yqjdataset/test/images`)
- **类别数**: 47个类别 (0-46)
- **标注格式**: YOLO格式 (txt文件)

## 文件结构

```
dan113/
├── yolov5-master/
│   ├── data/
│   │   └── yqjdataset.yaml          # YOLOv5数据集配置文件
│   ├── train.py                     # 原始训练脚本
│   ├── val.py                       # 原始验证脚本
│   └── detect.py                    # 原始检测脚本
├── yqjdataset/
│   ├── train/
│   │   ├── images/                  # 训练图像
│   │   └── labels/                  # 训练标签
│   ├── test/
│   │   ├── images/                  # 测试图像
│   │   └── labels/                  # 测试标签
│   └── data.yaml                    # 原始数据集配置
├── train_yqjdataset.py              # 简化的训练脚本
├── validate_yqjdataset.py           # 简化的验证脚本
├── detect_yqjdataset.py             # 简化的检测脚本
└── README_yqjdataset.md             # 本文件
```

## 使用方法

### 1. 训练模型

使用简化脚本训练：
```bash
python train_yqjdataset.py
```

或者直接使用YOLOv5原始脚本：
```bash
cd yolov5-master
python train.py --data data/yqjdataset.yaml --weights yolov5s.pt --img 640 --epochs 100 --batch-size 16
```

### 2. 验证模型

使用简化脚本验证：
```bash
python validate_yqjdataset.py --weights runs/train/yqjdataset_exp/weights/best.pt
```

或者直接使用YOLOv5原始脚本：
```bash
cd yolov5-master
python val.py --data data/yqjdataset.yaml --weights runs/train/yqjdataset_exp/weights/best.pt --img 640
```

### 3. 目标检测

使用简化脚本检测：
```bash
python detect_yqjdataset.py --source yqjdataset/test/images --weights runs/train/yqjdataset_exp/weights/best.pt
```

或者直接使用YOLOv5原始脚本：
```bash
cd yolov5-master
python detect.py --weights runs/train/yqjdataset_exp/weights/best.pt --source ../yqjdataset/test/images --img 640
```

## 训练参数说明

- `--data`: 数据集配置文件路径
- `--weights`: 预训练权重文件路径
- `--img`: 输入图像尺寸 (默认640)
- `--epochs`: 训练轮数 (默认100)
- `--batch-size`: 批次大小 (默认16)
- `--device`: 使用的设备 (0表示GPU 0, cpu表示CPU)

## 输出结果

训练完成后，结果将保存在：
- `yolov5-master/runs/train/yqjdataset_exp/`
  - `weights/best.pt`: 最佳模型权重
  - `weights/last.pt`: 最后一轮权重
  - `results.png`: 训练曲线图
  - `confusion_matrix.png`: 混淆矩阵

验证结果将保存在：
- `yolov5-master/runs/val/yqjdataset_val/`

检测结果将保存在：
- `yolov5-master/runs/detect/yqjdataset_detect/`

## 快速开始

### 方法1: 使用一键启动脚本
```bash
python start_training.py
```

### 方法2: 使用简化脚本
```bash
python train_yqjdataset.py
```

### 方法3: 直接使用YOLOv5命令
```bash
cd yolov5-master
python train.py --data data/yqjdataset.yaml --weights yolov5s.pt --img 640 --epochs 100
```

## 配置验证

在开始训练前，可以运行配置测试脚本：
```bash
python test_yqjdataset_config.py
```

这个脚本会验证：
- ✅ 数据集路径是否正确
- ✅ 配置文件是否有效
- ✅ 图像和标签是否匹配
- ✅ 显示数据集统计信息

## 注意事项

1. 确保已安装YOLOv5的依赖包：
   ```bash
   cd yolov5-master
   pip install -r requirements.txt
   ```

2. 如果使用GPU训练，确保已安装CUDA和PyTorch GPU版本

3. 根据你的硬件配置调整batch-size参数

4. 训练时间取决于硬件配置，GPU训练会显著快于CPU训练

## 已完成的配置

✅ **数据集配置文件**: `yolov5-master/data/yqjdataset.yaml`
✅ **训练脚本**: `train_yqjdataset.py`
✅ **验证脚本**: `validate_yqjdataset.py`
✅ **检测脚本**: `detect_yqjdataset.py`
✅ **配置测试**: `test_yqjdataset_config.py`
✅ **一键启动**: `start_training.py`

## 类别映射

当前配置使用数字作为类别名称 (0-46)。如果需要使用更有意义的类别名称，可以修改 `yolov5-master/data/yqjdataset.yaml` 文件中的 `names` 部分。
